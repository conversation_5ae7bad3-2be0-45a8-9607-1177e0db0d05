import React from "react";
import Container from "@/components/core/container";
import SectionMenu from "./section-menu";
import { DeleteSectionProps } from "@/actions/section";

type Props = {
  children: React.ReactNode;
  section: DeleteSectionProps;
};

const SectionContainers = ({ children, section }: Props) => {
  return (
    <section className="relative py-16 group overflow-hidden">
      <SectionMenu section={section} />
      <Container asChild>{children}</Container>
    </section>
  );
};

export default SectionContainers;
