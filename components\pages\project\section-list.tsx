import React from "react";
import SectionButton from "./section-button";
import { $Enums, Project, Section, Text } from "@prisma/client";
import TextEditor from "./sections/text-section/text-editor";
import SectionContainers from "./section-containers";

type Props = {
  sections: (Section & { text: Text | null })[];
  projectId: Project["id"];
};

const SectionList = ({ sections, projectId }: Props) => {
  return (
    <ul>
      {sections.map(({ id, index, type, text, projectId }) => (
        <li key={id} className="">
          <SectionButton index={index} projectId={projectId} />
          <SectionContainers section={{ id, index, projectId }}>
            {type === $Enums.SectionType.TEXT && text && (
              <TextEditor text={text} />
            )}
          </SectionContainers>
        </li>
      ))}

      <li>
        <SectionButton index={sections.length} projectId={projectId} />
      </li>
    </ul>
  );
};

export default SectionList;
