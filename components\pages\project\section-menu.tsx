"use client";

import React from "react";
import { Trash2, Move, Settings, Copy } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import DeleteDialog from "@/components/core/delete-dialog";
import { deleteSectionAction, DeleteSectionProps } from "@/actions/section";

type Props = {
  section: DeleteSectionProps;
};

const SectionMenu = ({ section }: Props) => {
  return (
    <div
      className={cn(
        "absolute top-4 right-4 opacity-0 group-hover/section:opacity-100",
        "transition-all duration-300 transform translate-x-2 group-hover/section:translate-x-0",
        "bg-background/95 backdrop-blur-sm border border-border rounded-lg p-1 shadow-lg",
        "flex items-center gap-1"
      )}
    >
      {/* Move/Drag handle */}
      <Button
        variant="ghost"
        size="sm"
        className="h-8 w-8 p-0 hover:bg-muted cursor-grab active:cursor-grabbing"
        title="Drag to reorder"
      >
        <Move className="h-4 w-4" />
      </Button>

      {/* Settings */}
      <Button
        variant="ghost"
        size="sm"
        className="h-8 w-8 p-0 hover:bg-muted"
        title="Section settings"
      >
        <Settings className="h-4 w-4" />
      </Button>

      {/* Duplicate */}
      <Button
        variant="ghost"
        size="sm"
        className="h-8 w-8 p-0 hover:bg-muted"
        title="Duplicate section"
      >
        <Copy className="h-4 w-4" />
      </Button>

      {/* Delete */}
      <DeleteDialog action={deleteSectionAction.bind(null, section)}>
        <Button
          variant="ghost"
          size="sm"
          className="h-8 w-8 p-0 hover:bg-destructive hover:text-destructive-foreground"
          title="Delete section"
        >
          <Trash2 className="h-4 w-4" />
        </Button>
      </DeleteDialog>
    </div>
  );
};

export default SectionMenu;
