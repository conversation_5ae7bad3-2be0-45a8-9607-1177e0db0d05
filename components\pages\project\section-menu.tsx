import React from "react";
import { Trash2 } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import DeleteDialog from "@/components/core/delete-dialog";
import { deleteSectionAction, DeleteSectionProps } from "@/actions/section";

type Props = {
  section: DeleteSectionProps;
};

const SectionMenu = ({ section }: Props) => {
  return (
    <div
      className={cn(
        "absolute top-1/2 -translate-y-1/2 -right-12",
        "group-hover:-translate-x-16",
        "duration-500",
        "bg-blue-500 p-1 flex flex-col gap-1 rounded"
      )}
    >
      <DeleteDialog action={deleteSectionAction.bind(null, section)}>
        <Button variant="destructive">
          <Trash2 className="size-4" />
        </Button>
      </DeleteDialog>
    </div>
  );
};

export default SectionMenu;
