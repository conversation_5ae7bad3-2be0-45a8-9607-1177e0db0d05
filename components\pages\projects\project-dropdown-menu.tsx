import React from "react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { But<PERSON> } from "@/components/ui/button";
import { Ellipsis, Trash2Icon } from "lucide-react";
import DeleteDialog from "../../core/delete-dialog";
import { deleteProjectAction } from "@/actions/projects";
import { Project } from "@prisma/client";

type Props = {
  id: Project["id"];
  title: string;
};

const ProjectDropdownMenu = ({ id }: Props) => {
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button className="absolute top-4 right-4">
          <Ellipsis className="size-4" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent className="bg-primary" align="end">
        <DropdownMenuItem asChild>
          <DeleteDialog action={deleteProjectAction.bind(null, id)}>
            <Button className="w-full">
              <Trash2Icon className="size-4" />
              <span>Delete</span>
            </Button>
          </DeleteDialog>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export default ProjectDropdownMenu;
