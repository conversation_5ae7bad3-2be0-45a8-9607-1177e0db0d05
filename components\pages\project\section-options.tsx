"use client";

import React from "react";
import { Button } from "@/components/ui/button";
import { CameraIcon, Image, ImagesIcon, LetterText } from "lucide-react";
import { createSectionAction } from "@/actions/section";
import { $Enums, Section } from "@prisma/client";
import { toast } from "sonner";

type Props = {
  index: Section["index"];
  projectId: Section["projectId"];
};

const SectionOptions = ({ index, projectId }: Props) => {
  const options = [
    {
      id: 1,
      type: $Enums.SectionType.TEXT,
      icon: LetterText,
      label: "Text",
    },
    {
      id: 2,
      type: $Enums.SectionType.IMAGE,
      icon: Image,
      label: "Image",
    },
    {
      id: 3,
      type: $Enums.SectionType.VIDEO,
      icon: CameraIcon,
      label: "Video",
    },
    {
      id: 4,
      type: $Enums.SectionType.TEXTIMAGE,
      icon: ImagesIcon,
      label: "Text Image",
    },
  ];

  const handleSectionCreate = async (type: $Enums.SectionType) => {
    const { data, message, error } = await createSectionAction({
      projectId: projectId,
      index: index,
      type,
    });

    if (data) {
      toast.success(message);
    }

    if (error) {
      toast.error(message);
    }
  };

  return (
    <ul className="py-8 grid grid-cols-4 gap-4">
      {options.map((option) => (
        <li key={option.type} className="flex justify-center items-center">
          <Button
            className="group flex flex-col items-center gap-2"
            variant="plain"
            size="none"
            onClick={() => handleSectionCreate(option.type)}
          >
            <div className="group-hover:bg-accent p-4">
              <option.icon className="size-10" />
            </div>
            <span className="text-muted-foreground group-hover:text-foreground text-xs">
              {option.label}
            </span>
          </Button>
        </li>
      ))}
    </ul>
  );
};

export default SectionOptions;
